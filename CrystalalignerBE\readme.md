# CrystalAligner Backend

## Development Setup

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose
- .NET 7.0 SDK

### Running Development Environment

```bash
# Start the development environment
docker-compose up -d

# Stop the development environment
docker-compose down
```

The development environment will start:
- Database: SQL Server on port 1433
- API: .NET Core application on port 5000
- Frontend: Pre-built frontend image on port 80

### Development URLs
- API: http://localhost:5000
- Frontend: http://localhost

## Production Setup

### Running Production Environment

```bash
# Start the production environment
docker-compose -f docker-compose.prod.yml up -d

# Stop the production environment
docker-compose -f docker-compose.prod.yml down
```

The production environment will start:
- Database: SQL Server on port 1434
- API: Pre-built API image on port 5001 (localhost only)
- Frontend: Pre-built frontend image on port 8080 (localhost only)

### Production URLs
- API: https://api.buraxta.com (externally accessible)
- Frontend: https://api.buraxta.com (externally accessible)

## CORS Configuration

The application is configured to allow CORS requests from:
- https://api.buraxta.com (production)
- http://localhost:8080 (development)
- http://localhost (development)

## Environment Variables

### Database Connection
- `ConnectionStrings__DefaultConnection`: Database connection string

### Application Environment
- `ASPNETCORE_ENVIRONMENT`: Development, Staging, or Production

## Docker Images

### Backend API
- Development: Built from source using Dockerfile
- Production: `buraxtaa/crystalback:latest`

### Frontend
- Both environments: `buraxtaa/crystaldoctor:latest`

## Nginx Configuration

The production setup uses Nginx for:
- Static file serving
- API proxy routing
- SSL termination
- Security headers
- Gzip compression

Configuration files are located in the `nginx_config` directory.
